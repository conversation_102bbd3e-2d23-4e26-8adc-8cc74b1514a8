// CMS Content Types for Hitez Agency
// This structure allows for easy migration to a real CMS later

export interface SiteMetadata {
	title: string;
	description: string;
	keywords: string;
	ogTitle: string;
	ogDescription: string;
	ogType: string;
}

export interface BrandInfo {
	name: string;
	tagline: string;
	description: string;
	logo: string;
}

export interface NavigationItem {
	href: string;
	label: string;
}

export interface ContactInfo {
	email: string;
	phone: string;
	address: string;
}

export interface SocialLink {
	href: string;
	label: string;
	icon: string;
}

export interface Service {
	id: string;
	title: string;
	subtitle: string;
	description: string;
	icon: string;
	features: string[];
	process: string[];
	examples: string[];
}

export interface Project {
	id: string;
	title: string;
	subtitle: string;
	description: string;
	category: string;
	tags: string[];
	year: string;
	client: string;
	image: string;
	featured: boolean;
	overview?: string;
	challenge?: string;
	solution?: string;
	result?: string;
	gallery?: string[];
	technologies?: string[];
	duration?: string;
	team?: string;
	hero?: {
		image: string;
		video?: string | null;
	};
	testimonial?: {
		quote: string;
		author: string;
		role: string;
	};
}

export interface ProjectCategory {
	id: string;
	label: string;
}

export interface Statistic {
	number: number;
	suffix: string;
	label: string;
}

export interface Value {
	title: string;
	description: string;
	icon: string;
}

export interface Approach {
	title: string;
	description: string;
	icon: string;
}

export interface HeroSection {
	title: {
		line1: string;
		line2: string;
	};
	subtitle: string;
	ctaText: string;
	ctaLink: string;
}

export interface CTASection {
	title: string;
	subtitle: string;
	primaryCTA: {
		text: string;
		link: string;
	};
	secondaryCTA: {
		text: string;
		link: string;
	};
}

export interface FormOption {
	value: string;
	label: string;
}

export interface ProblemPoint {
	icon: string;
	title: string;
	description: string;
}

export interface ProblemSection {
	title: string;
	subtitle: string;
	points: ProblemPoint[];
	solution: {
		title: string;
		description: string;
	};
}

export interface VisionValue {
	icon: string;
	title: string;
	description: string;
}

export interface VisionSection {
	title: string;
	subtitle: string;
	values: VisionValue[];
	team: {
		title: string;
		description: string;
		stats: Statistic[];
	};
}

export interface DemoProject {
	id: string;
	title: string;
	category: string;
	description: string;
	technologies: string[];
	innovation: string;
	image: string;
	learnings: string[];
}

export interface DemosSection {
	title: string;
	subtitle: string;
	description: string;
	featured: DemoProject[];
}

export interface TraditionalService {
	icon: string;
	title: string;
	description: string;
}

export interface TraditionalExample {
	title: string;
	description: string;
	impact: string;
}

export interface TraditionalSection {
	title: string;
	subtitle: string;
	description: string;
	services: TraditionalService[];
	examples?: TraditionalExample[];
}

export interface PageContent {
	title: string;
	description: string;
	hero?: HeroSection;
	problem?: ProblemSection;
	vision?: VisionSection;
	demos?: DemosSection;
	traditional?: TraditionalSection;
	cta?: CTASection;
}

export interface CMSContent {
	site: SiteMetadata;
	brand: BrandInfo;
	navigation: NavigationItem[];
	contact: ContactInfo;
	social: SocialLink[];
	services: Service[];
	projects: Project[];
	projectCategories: ProjectCategory[];
	stats: Statistic[];
	values: Value[];
	approaches: Approach[];
	pages: {
		home: PageContent;
		about: PageContent;
		services: PageContent;
		work: PageContent;
		contact: PageContent;
	};
	forms: {
		contact: {
			projectTypes: FormOption[];
			budgetRanges: FormOption[];
			timelines: FormOption[];
		};
	};
}
