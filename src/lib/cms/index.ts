// CMS Export Module
// Centralized content management for easy migration to real CMS

export { cmsContent } from './content.js';
export type * from './types.js';

// Helper functions for accessing CMS content
import { cmsContent } from './content.js';
import type { Project, Service, ProjectCategory } from './types.js';

/**
 * Get all projects or filter by category
 */
export function getProjects(category?: string): Project[] {
	if (!category || category === 'all') {
		return cmsContent.projects;
	}
	return cmsContent.projects.filter(project => project.category === category);
}

/**
 * Get featured projects only
 */
export function getFeaturedProjects(): Project[] {
	return cmsContent.projects.filter(project => project.featured);
}

/**
 * Get a specific project by ID
 */
export function getProject(id: string): Project | undefined {
	return cmsContent.projects.find(project => project.id === id);
}

/**
 * Get all services
 */
export function getServices(): Service[] {
	return cmsContent.services;
}

/**
 * Get a specific service by ID
 */
export function getService(id: string): Service | undefined {
	return cmsContent.services.find(service => service.id === id);
}

/**
 * Get project categories for filtering
 */
export function getProjectCategories(): ProjectCategory[] {
	return cmsContent.projectCategories;
}

/**
 * Get site metadata
 */
export function getSiteMetadata() {
	return cmsContent.site;
}

/**
 * Get brand information
 */
export function getBrandInfo() {
	return cmsContent.brand;
}

/**
 * Get navigation items
 */
export function getNavigation() {
	return cmsContent.navigation;
}

/**
 * Get contact information
 */
export function getContactInfo() {
	return cmsContent.contact;
}

/**
 * Get social links
 */
export function getSocialLinks() {
	return cmsContent.social;
}

/**
 * Get page content by page name
 */
export function getPageContent(pageName: keyof typeof cmsContent.pages) {
	return cmsContent.pages[pageName];
}

/**
 * Get statistics for about page
 */
export function getStats() {
	return cmsContent.stats;
}

/**
 * Get company values
 */
export function getValues() {
	return cmsContent.values;
}

/**
 * Get approaches for home page
 */
export function getApproaches() {
	return cmsContent.approaches;
}

/**
 * Get form options for contact page
 */
export function getFormOptions() {
	return cmsContent.forms.contact;
}
