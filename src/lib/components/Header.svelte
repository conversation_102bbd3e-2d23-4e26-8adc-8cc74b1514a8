<script lang="ts">
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import { getBrandInfo, getNavigation } from '$lib/cms';

	let isMenuOpen = false;
	let isScrolled = false;

	const brand = getBrandInfo();
	const navItems = getNavigation();

	onMount(() => {
		const handleScroll = () => {
			isScrolled = window.scrollY > 50;
		};

		window.addEventListener('scroll', handleScroll);
		return () => window.removeEventListener('scroll', handleScroll);
	});

	const toggleMenu = () => {
		isMenuOpen = !isMenuOpen;
	};

	const closeMenu = () => {
		isMenuOpen = false;
	};
</script>

<header
	class="fixed top-0 left-0 right-0 z-50 transition-all duration-300 {isScrolled
		? 'bg-primary/95 backdrop-blur-md border-b border-accent/20'
		: 'bg-transparent'}"
>
	<nav class="container mx-auto px-6 py-4">
		<div class="flex items-center justify-between">
			<!-- Logo -->
			<a href="/" class="flex items-center space-x-2">
				<div class="text-2xl font-bold text-accent font-heading">{brand.name}</div>
			</a>

			<!-- Desktop Navigation -->
			<div class="hidden md:flex items-center space-x-8">
				{#each navItems as item}
					<a
						href={item.href}
						class="text-text hover:text-accent transition-colors duration-300 font-medium relative group"
						class:text-accent={$page.url.pathname === item.href}
					>
						{item.label}
						<span
							class="absolute -bottom-1 left-0 w-0 h-0.5 bg-accent transition-all duration-300 group-hover:w-full"
							class:w-full={$page.url.pathname === item.href}
						></span>
					</a>
				{/each}
			</div>

			<!-- CTA Button -->
			<div class="hidden md:block">
				<a href="/contact" class="btn btn-primary"> Start a Project </a>
			</div>

			<!-- Mobile Menu Button -->
			<button
				class="md:hidden flex flex-col items-center justify-center w-8 h-8 space-y-1.5"
				on:click={toggleMenu}
				aria-label="Toggle menu"
			>
				<span
					class="w-6 h-0.5 bg-text transition-all duration-300 {isMenuOpen
						? 'rotate-45 translate-y-2'
						: ''}"
				></span>
				<span
					class="w-6 h-0.5 bg-text transition-all duration-300 {isMenuOpen ? 'opacity-0' : ''}"
				></span>
				<span
					class="w-6 h-0.5 bg-text transition-all duration-300 {isMenuOpen
						? '-rotate-45 -translate-y-2'
						: ''}"
				></span>
			</button>
		</div>

		<!-- Mobile Menu -->
		{#if isMenuOpen}
			<div
				class="md:hidden absolute top-full left-0 right-0 bg-primary/95 backdrop-blur-md border-b border-accent/20 py-6"
			>
				<div class="container mx-auto px-6">
					<div class="flex flex-col space-y-4">
						{#each navItems as item}
							<a
								href={item.href}
								class="text-text hover:text-accent transition-colors duration-300 font-medium py-2"
								class:text-accent={$page.url.pathname === item.href}
								on:click={closeMenu}
							>
								{item.label}
							</a>
						{/each}
						<div class="pt-4">
							<a href="/contact" class="btn btn-primary w-full" on:click={closeMenu}>
								Start a Project
							</a>
						</div>
					</div>
				</div>
			</div>
		{/if}
	</nav>
</header>

<style>
	/* Additional custom styles if needed */
</style>
