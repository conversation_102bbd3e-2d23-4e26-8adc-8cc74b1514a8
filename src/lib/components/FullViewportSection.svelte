<script lang="ts">
	import { onMount } from 'svelte';

	export let id: string;
	export let className: string = '';
	export let background: string = '';
	export let centered: boolean = true;
	export let padding: boolean = true;

	let sectionElement: HTMLElement;
	let isVisible = false;

	onMount(() => {
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						isVisible = true;
						// Add staggered animation delays to child elements
						const animatedElements = entry.target.querySelectorAll('.section-transition');
						animatedElements.forEach((el, index) => {
							setTimeout(() => {
								el.classList.add('visible');
							}, index * 100);
						});
					}
				});
			},
			{
				threshold: 0.3,
				rootMargin: '0px 0px -20% 0px'
			}
		);

		if (sectionElement) {
			observer.observe(sectionElement);
		}

		return () => {
			if (observer) {
				observer.disconnect();
			}
		};
	});
</script>

<section
	{id}
	bind:this={sectionElement}
	class="fullpage-section {className}"
	class:items-center={centered}
	class:justify-center={centered}
	class:px-6={padding}
	style={background ? `background: ${background}` : ''}
>
	<div class="w-full" class:max-w-7xl={padding} class:mx-auto={padding}>
		<slot {isVisible} />
	</div>
</section>

<style>
	.fullpage-section {
		position: relative;
		overflow: hidden;
	}

	.fullpage-section::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: inherit;
		z-index: -1;
	}
</style>
