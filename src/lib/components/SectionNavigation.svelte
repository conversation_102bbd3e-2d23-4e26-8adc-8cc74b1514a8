<script lang="ts">
	import { onMount } from 'svelte';

	export let sections: Array<{ id: string; label: string }> = [];
	
	let currentSection = '';
	let observer: IntersectionObserver;

	onMount(() => {
		// Set up intersection observer to track current section
		observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
						currentSection = entry.target.id;
					}
				});
			},
			{
				threshold: [0.5],
				rootMargin: '-10% 0px -10% 0px'
			}
		);

		// Observe all sections
		sections.forEach((section) => {
			const element = document.getElementById(section.id);
			if (element) {
				observer.observe(element);
			}
		});

		return () => {
			if (observer) {
				observer.disconnect();
			}
		};
	});

	function scrollToSection(sectionId: string) {
		const element = document.getElementById(sectionId);
		if (element) {
			element.scrollIntoView({
				behavior: 'smooth',
				block: 'start'
			});
		}
	}
</script>

<nav class="section-nav" aria-label="Page sections">
	{#each sections as section}
		<button
			class="section-nav-dot"
			class:active={currentSection === section.id}
			data-label={section.label}
			aria-label={`Go to ${section.label} section`}
			on:click={() => scrollToSection(section.id)}
		/>
	{/each}
</nav>
