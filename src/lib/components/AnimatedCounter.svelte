<script lang="ts">
	import { onMount } from 'svelte';

	interface Props {
		target: number;
		duration?: number;
		suffix?: string;
		prefix?: string;
	}

	let { target, duration = 2000, suffix = '', prefix = '' }: Props = $props();

	let current = $state(0);
	let element: HTMLElement;

	onMount(() => {
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						animateCounter();
						observer.unobserve(entry.target);
					}
				});
			},
			{ threshold: 0.5 }
		);

		if (element) {
			observer.observe(element);
		}

		return () => observer.disconnect();
	});

	function animateCounter() {
		const startTime = Date.now();
		const startValue = 0;

		function update() {
			const elapsed = Date.now() - startTime;
			const progress = Math.min(elapsed / duration, 1);

			// Easing function for smooth animation
			const easeOutQuart = 1 - Math.pow(1 - progress, 4);
			current = Math.floor(startValue + (target - startValue) * easeOutQuart);

			if (progress < 1) {
				requestAnimationFrame(update);
			} else {
				current = target;
			}
		}

		requestAnimationFrame(update);
	}
</script>

<span bind:this={element} class="font-bold text-accent">
	{prefix}{current}{suffix}
</span>
