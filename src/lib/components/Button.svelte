<script lang="ts">
	interface Props {
		variant?: 'primary' | 'outline' | 'ghost';
		size?: 'sm' | 'md' | 'lg';
		href?: string;
		type?: 'button' | 'submit' | 'reset';
		disabled?: boolean;
		class?: string;
		onclick?: () => void;
		children: any;
	}

	let {
		variant = 'primary',
		size = 'md',
		href,
		type = 'button',
		disabled = false,
		class: className = '',
		onclick,
		children
	}: Props = $props();

	const baseClasses = 'btn inline-flex items-center justify-center font-heading font-semibold transition-all duration-300';
	
	const variantClasses = {
		primary: 'btn-primary',
		outline: 'btn-outline',
		ghost: 'bg-transparent text-accent hover:bg-accent/10'
	};

	const sizeClasses = {
		sm: 'px-4 py-2 text-sm',
		md: 'px-6 py-3 text-base',
		lg: 'px-8 py-4 text-lg'
	};

	const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
</script>

{#if href}
	<a {href} class={classes} class:opacity-50={disabled} class:pointer-events-none={disabled}>
		{@render children()}
	</a>
{:else}
	<button
		{type}
		{disabled}
		class={classes}
		on:click={onclick}
	>
		{@render children()}
	</button>
{/if}
