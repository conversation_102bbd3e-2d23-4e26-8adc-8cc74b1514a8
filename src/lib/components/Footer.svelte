<script lang="ts">
	import { getBrandInfo, getNavigation, getSocialLinks, getContactInfo } from '$lib/cms';

	const currentYear = new Date().getFullYear();
	const brand = getBrandInfo();
	const navigationLinks = getNavigation();
	const socialLinks = getSocialLinks();
	const contactInfo = getContactInfo();


</script>

<footer class="bg-darker border-t border-accent/20 mt-20">
	<div class="container mx-auto px-6 py-16">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
			<!-- Column 1: Logo and Slogan -->
			<div class="space-y-4">
				<div class="text-2xl font-bold text-accent font-heading">{brand.name}</div>
				<p class="text-text-muted text-sm leading-relaxed">
					{brand.tagline}
				</p>
				<p class="text-text-muted text-sm">
					{brand.description}
				</p>
			</div>

			<!-- Column 2: Navigation Links -->
			<div class="space-y-4">
				<h4 class="text-text font-semibold font-heading">Navigation</h4>
				<ul class="space-y-2">
					{#each navigationLinks as link}
						<li>
							<a
								href={link.href}
								class="text-text-muted hover:text-accent transition-colors duration-300 text-sm"
							>
								{link.label}
							</a>
						</li>
					{/each}
				</ul>
			</div>

			<!-- Column 3: Social Media -->
			<div class="space-y-4">
				<h4 class="text-text font-semibold font-heading">Follow Us</h4>
				<div class="flex space-x-4">
					{#each socialLinks as social}
						<a
							href={social.href}
							target="_blank"
							rel="noopener noreferrer"
							class="w-8 h-8 flex items-center justify-center hover:text-accent transition-colors duration-300"
							aria-label={social.label}
						>
							<svg class="w-5 h-5 fill-current" viewBox="0 0 24 24">
								<path d={social.icon} />
							</svg>
						</a>
					{/each}
				</div>
			</div>

			<!-- Column 4: Contact Info -->
			<div class="space-y-4">
				<h4 class="text-text font-semibold font-heading">Contact</h4>
				<div class="space-y-2 text-sm">
					<a
						href="mailto:{contactInfo.email}"
						class="text-text-muted hover:text-accent transition-colors duration-300 block"
					>
						{contactInfo.email}
					</a>
					<a
						href="tel:{contactInfo.phone}"
						class="text-text-muted hover:text-accent transition-colors duration-300 block"
					>
						{contactInfo.phone}
					</a>
					<p class="text-text-muted">{contactInfo.address}</p>
				</div>
			</div>
		</div>

		<!-- Bottom Section -->
		<div class="border-t border-accent/20 mt-12 pt-8">
			<div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
				<p class="text-text-muted text-sm">
					© {currentYear} Hitez. All rights reserved.
				</p>
				<div class="flex space-x-6 text-sm">
					<a href="/privacy" class="text-text-muted hover:text-accent transition-colors duration-300">
						Privacy Policy
					</a>
					<a href="/terms" class="text-text-muted hover:text-accent transition-colors duration-300">
						Terms of Service
					</a>
				</div>
			</div>
		</div>
	</div>
</footer>
