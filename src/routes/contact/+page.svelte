<script lang="ts">
	import Button from '$lib/components/Button.svelte';
	import { onMount } from 'svelte';

	let form = $state({
		name: '',
		email: '',
		company: '',
		projectType: '',
		budget: '',
		timeline: '',
		message: ''
	});

	let isSubmitting = $state(false);
	let submitStatus = $state<'idle' | 'success' | 'error'>('idle');

	const projectTypes = [
		'Interactive Installation',
		'AR/VR Experience',
		'Projection Mapping',
		'Creative Technology Consulting',
		'Event Activation',
		'Other'
	];

	const budgetRanges = [
		'Under $25k',
		'$25k - $50k',
		'$50k - $100k',
		'$100k - $250k',
		'$250k+',
		"Let's discuss"
	];

	const timelines = ['ASAP', '1-2 months', '3-6 months', '6+ months', 'Flexible'];

	async function handleSubmit(event: Event) {
		event.preventDefault();
		isSubmitting = true;

		try {
			// Simulate form submission
			await new Promise((resolve) => setTimeout(resolve, 2000));

			// In a real app, you'd send this to your backend
			console.log('Form submitted:', form);

			submitStatus = 'success';

			// Reset form
			form = {
				name: '',
				email: '',
				company: '',
				projectType: '',
				budget: '',
				timeline: '',
				message: ''
			};
		} catch (error) {
			submitStatus = 'error';
		} finally {
			isSubmitting = false;
		}
	}

	onMount(() => {
		// Reset status after 5 seconds
		const timer = setTimeout(() => {
			if (submitStatus !== 'idle') {
				submitStatus = 'idle';
			}
		}, 5000);

		return () => clearTimeout(timer);
	});
</script>

<svelte:head>
	<title>Contact Us - Hitez</title>
	<meta
		name="description"
		content="Ready to create something extraordinary? Get in touch with our team to discuss your next creative technology project."
	/>
</svelte:head>

<!-- Hero Section -->
<section
	class="from-primary to-surface relative overflow-hidden bg-gradient-to-br px-6 pt-32 pb-20"
>
	<div class="absolute inset-0">
		<div class="bg-accent/5 float absolute top-1/4 left-1/4 h-64 w-64 rounded-full blur-3xl"></div>
		<div
			class="bg-accent-secondary/5 float absolute right-1/4 bottom-1/4 h-96 w-96 rounded-full blur-3xl"
			style="animation-delay: -2s;"
		></div>
	</div>

	<div class="relative z-10 container mx-auto max-w-4xl text-center">
		<h1 class="fade-in mb-8 text-5xl font-black md:text-7xl">
			Let's Create <span class="text-accent gradient-text">Together</span>
		</h1>
		<p class="text-text-muted slide-up mx-auto mb-8 max-w-3xl text-xl md:text-2xl">
			Have a crazy idea? We live for that. Let's turn your vision into an unforgettable experience.
		</p>
	</div>
</section>

<!-- Contact Form & Info -->
<section class="bg-surface-elevated px-6 py-20">
	<div class="container mx-auto max-w-6xl">
		<div class="grid grid-cols-1 gap-16 lg:grid-cols-2">
			<!-- Contact Form -->
			<div class="slide-up">
				<h2 class="mb-8 text-3xl font-bold">Start Your Project</h2>

				{#if submitStatus === 'success'}
					<div class="bg-accent/20 border-accent/40 mb-8 rounded-lg border p-6">
						<div class="flex items-center">
							<svg
								class="text-accent mr-3 h-6 w-6"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M5 13l4 4L19 7"
								></path>
							</svg>
							<div>
								<h3 class="text-accent font-bold">Message Sent!</h3>
								<p class="text-text-muted text-sm">We'll get back to you within 24 hours.</p>
							</div>
						</div>
					</div>
				{/if}

				{#if submitStatus === 'error'}
					<div class="mb-8 rounded-lg border border-red-500/40 bg-red-500/20 p-6">
						<div class="flex items-center">
							<svg
								class="mr-3 h-6 w-6 text-red-400"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M6 18L18 6M6 6l12 12"
								></path>
							</svg>
							<div>
								<h3 class="font-bold text-red-400">Something went wrong</h3>
								<p class="text-text-muted text-sm">Please try again or email us directly.</p>
							</div>
						</div>
					</div>
				{/if}

				<form onsubmit={handleSubmit} class="space-y-6">
					<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
						<div>
							<label for="name" class="text-text mb-2 block text-sm font-medium">Name *</label>
							<input
								type="text"
								id="name"
								bind:value={form.name}
								required
								class="bg-dark border-accent/20 focus:border-accent text-text w-full rounded-lg border px-4 py-3 transition-colors duration-300 focus:outline-none"
								placeholder="Your name"
							/>
						</div>
						<div>
							<label for="email" class="text-text mb-2 block text-sm font-medium">Email *</label>
							<input
								type="email"
								id="email"
								bind:value={form.email}
								required
								class="bg-dark border-accent/20 focus:border-accent text-text w-full rounded-lg border px-4 py-3 transition-colors duration-300 focus:outline-none"
								placeholder="<EMAIL>"
							/>
						</div>
					</div>

					<div>
						<label for="company" class="text-text mb-2 block text-sm font-medium">Company</label>
						<input
							type="text"
							id="company"
							bind:value={form.company}
							class="bg-dark border-accent/20 focus:border-accent text-text w-full rounded-lg border px-4 py-3 transition-colors duration-300 focus:outline-none"
							placeholder="Your company name"
						/>
					</div>

					<div class="grid grid-cols-1 gap-6 md:grid-cols-3">
						<div>
							<label for="projectType" class="text-text mb-2 block text-sm font-medium"
								>Project Type</label
							>
							<select
								id="projectType"
								bind:value={form.projectType}
								class="bg-dark border-accent/20 focus:border-accent text-text w-full rounded-lg border px-4 py-3 transition-colors duration-300 focus:outline-none"
							>
								<option value="">Select type</option>
								{#each projectTypes as type}
									<option value={type}>{type}</option>
								{/each}
							</select>
						</div>
						<div>
							<label for="budget" class="text-text mb-2 block text-sm font-medium"
								>Budget Range</label
							>
							<select
								id="budget"
								bind:value={form.budget}
								class="bg-dark border-accent/20 focus:border-accent text-text w-full rounded-lg border px-4 py-3 transition-colors duration-300 focus:outline-none"
							>
								<option value="">Select budget</option>
								{#each budgetRanges as range}
									<option value={range}>{range}</option>
								{/each}
							</select>
						</div>
						<div>
							<label for="timeline" class="text-text mb-2 block text-sm font-medium">Timeline</label
							>
							<select
								id="timeline"
								bind:value={form.timeline}
								class="bg-dark border-accent/20 focus:border-accent text-text w-full rounded-lg border px-4 py-3 transition-colors duration-300 focus:outline-none"
							>
								<option value="">Select timeline</option>
								{#each timelines as time}
									<option value={time}>{time}</option>
								{/each}
							</select>
						</div>
					</div>

					<div>
						<label for="message" class="text-text mb-2 block text-sm font-medium"
							>Tell us about your project *</label
						>
						<textarea
							id="message"
							bind:value={form.message}
							required
							rows="6"
							class="bg-dark border-accent/20 focus:border-accent text-text w-full resize-none rounded-lg border px-4 py-3 transition-colors duration-300 focus:outline-none"
							placeholder="Describe your vision, goals, and any specific requirements..."
						></textarea>
					</div>

					<Button type="submit" variant="primary" size="lg" disabled={isSubmitting} class="w-full">
						{isSubmitting ? 'Sending...' : 'Send Message'}
					</Button>
				</form>
			</div>

			<!-- Contact Information -->
			<div class="slide-up" style="animation-delay: 0.2s;">
				<h2 class="mb-8 text-3xl font-bold">Get in Touch</h2>

				<div class="space-y-8">
					<!-- Contact Details -->
					<div>
						<h3 class="text-accent mb-4 text-xl font-bold">Contact Information</h3>
						<div class="space-y-4">
							<div class="flex items-center">
								<svg
									class="text-accent mr-4 h-5 w-5"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
									></path>
								</svg>
								<a
									href="mailto:<EMAIL>"
									class="text-text-muted hover:text-accent transition-colors duration-300"
								>
									<EMAIL>
								</a>
							</div>
							<div class="flex items-center">
								<svg
									class="text-accent mr-4 h-5 w-5"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
									></path>
								</svg>
								<a
									href="tel:+84123456789"
									class="text-text-muted hover:text-accent transition-colors duration-300"
								>
									+84 123 456 789
								</a>
							</div>
							<div class="flex items-center">
								<svg
									class="text-accent mr-4 h-5 w-5"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
									></path>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
									></path>
								</svg>
								<span class="text-text-muted">Ho Chi Minh City, Vietnam</span>
							</div>
						</div>
					</div>

					<!-- Response Time -->
					<div>
						<h3 class="text-accent mb-4 text-xl font-bold">Response Time</h3>
						<p class="text-text-muted leading-relaxed">
							We typically respond to all inquiries within 24 hours. For urgent projects, feel free
							to call us directly.
						</p>
					</div>

					<!-- Social Media -->
					<div>
						<h3 class="text-accent mb-4 text-xl font-bold">Follow Our Work</h3>
						<div class="flex space-x-4">
							<a
								href="https://instagram.com/hitez.agency"
								target="_blank"
								rel="noopener noreferrer"
								class="bg-dark border-accent/20 hover:border-accent hover:bg-accent/10 flex h-10 w-10 items-center justify-center rounded-lg border transition-all duration-300"
							>
								<svg class="text-accent h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
									<path
										d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"
									/>
								</svg>
							</a>
							<a
								href="https://linkedin.com/company/hitez"
								target="_blank"
								rel="noopener noreferrer"
								class="bg-dark border-accent/20 hover:border-accent hover:bg-accent/10 flex h-10 w-10 items-center justify-center rounded-lg border transition-all duration-300"
							>
								<svg class="text-accent h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
									<path
										d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
									/>
								</svg>
							</a>
							<a
								href="https://behance.net/hitez"
								target="_blank"
								rel="noopener noreferrer"
								class="bg-dark border-accent/20 hover:border-accent hover:bg-accent/10 flex h-10 w-10 items-center justify-center rounded-lg border transition-all duration-300"
							>
								<svg class="text-accent h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
									<path
										d="M6.938 4.503c.702 0 1.34.06 1.92.188.577.13 1.07.33 1.485.61.41.28.733.65.96 1.12.225.47.34 1.05.34 1.73 0 .74-.17 1.36-.507 1.86-.338.5-.837.9-1.502 1.22.906.26 1.576.72 2.022 1.37.448.66.665 1.45.665 2.36 0 .75-.13 1.39-.41 1.93-.28.55-.67 1-1.16 1.35-.48.348-1.05.6-1.67.76-.62.16-1.25.24-1.89.24H0V4.51h6.938v-.007zM3.495 8.42h2.876c.62 0 1.11-.13 1.47-.4.36-.27.54-.68.54-1.23 0-.55-.18-.96-.54-1.23-.36-.27-.85-.4-1.47-.4H3.495v3.26zm0 5.54h3.18c.3 0 .59-.04.86-.12.27-.08.51-.2.72-.36.21-.16.37-.37.48-.63.11-.26.17-.56.17-.89 0-.67-.17-1.18-.52-1.53-.35-.35-.83-.53-1.44-.53H3.495v4.06zm17.37-2.15c.02.32-.02.64-.08.95H16.93c.05.4.2.72.44.96.24.24.54.36.89.36.25 0 .48-.05.69-.15.21-.1.39-.25.54-.45h2.01c-.2.68-.61 1.24-1.17 1.68-.56.44-1.25.66-2.07.66-.48 0-.91-.08-1.3-.24-.39-.16-.73-.38-1.01-.67-.28-.29-.5-.63-.65-1.02-.15-.39-.23-.81-.23-1.26 0-.45.08-.87.23-1.26.15-.39.37-.73.65-1.02.28-.29.62-.51 1.01-.67.39-.16.82-.24 1.3-.24.45 0 .86.07 1.23.22.37.15.69.36.96.63.27.27.48.58.63.94.15.36.23.75.23 1.17zm-2.01-.8c0-.25-.09-.47-.26-.66-.17-.19-.39-.28-.68-.28-.14 0-.27.02-.39.07-.12.05-.23.12-.32.21-.09.09-.16.2-.21.32-.05.12-.08.25-.09.39h1.95z"
									/>
								</svg>
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
