<script lang="ts">
	import Button from '$lib/components/Button.svelte';
	import FullViewportSection from '$lib/components/FullViewportSection.svelte';
	import SectionNavigation from '$lib/components/SectionNavigation.svelte';
	import { getServices, getPageContent } from '$lib/cms';

	const services = getServices();
	const pageContent = getPageContent('home');

	const sections = [
		{ id: 'hero', label: 'Home' },
		{ id: 'problem', label: 'Challenge' },
		{ id: 'vision', label: 'Vision' },
		{ id: 'demos', label: 'Innovation' },
		{ id: 'services', label: 'Services' },
		{ id: 'traditional', label: 'Traditional' },
		{ id: 'contact', label: 'Contact' }
	];
</script>

<svelte:head>
	<title>{pageContent.title}</title>
	<meta name="description" content={pageContent.description} />
</svelte:head>

<div class="fullpage-container">
	<!-- Section Navigation -->
	<SectionNavigation {sections} />

	<!-- Hero Section -->
	<FullViewportSection
		id="hero"
		background="linear-gradient(135deg, var(--color-primary) 0%, var(--color-surface) 100%)"
	>
		<div slot="default" class="text-center">
			<!-- Background Elements -->
			<div class="pointer-events-none absolute inset-0">
				<div
					class="bg-accent/5 absolute top-1/4 left-1/4 h-64 w-64 animate-pulse rounded-full blur-3xl"
				></div>
				<div
					class="bg-accent-secondary/5 absolute right-1/4 bottom-1/4 h-96 w-96 animate-pulse rounded-full blur-3xl delay-1000"
				></div>
			</div>

			<div class="relative z-10 mx-auto max-w-5xl">
				<h1 class="section-transition heading-display text-balanced heading-spacing">
					<span class="block">{pageContent.hero?.title.line1}</span>
					<span class="accent-text-gradient block">{pageContent.hero?.title.line2}</span>
				</h1>
				<p
					class="section-transition text-xl-lead text-text-muted section-spacing mx-auto inline-block max-w-3xl"
				>
					{pageContent.hero?.subtitle}
				</p>
				<div class="section-transition">
					<Button href="#vision" variant="primary" size="lg">
						{pageContent.hero?.ctaText}
					</Button>
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Problem/Solution Section -->
	<FullViewportSection id="problem" className="bg-surface-elevated">
		<div slot="default" class="text-center">
			<div class="mx-auto max-w-6xl">
				<h2 class="section-transition heading-section text-balanced heading-spacing">
					{@html pageContent.problem?.title}
				</h2>
				<p
					class="section-transition text-lg-lead text-text-muted section-spacing mx-auto inline-block max-w-3xl"
				>
					{pageContent.problem?.subtitle}
				</p>

				<!-- Problem Points -->
				<div class="mb-8 grid grid-cols-1 gap-8 md:grid-cols-3">
					{#each pageContent.problem?.points || [] as point, index}
						<div
							class="section-transition glass-effect rounded-xl p-8"
							style="animation-delay: {index * 0.1}s;"
						>
							<div class="mb-6 text-5xl">{point.icon}</div>
							<h3 class="accent-text heading-subsection mb-4">{point.title}</h3>
							<p class="text-text-muted leading-relaxed">{point.description}</p>
						</div>
					{/each}
				</div>

				<!-- Solution -->
				<div class="section-transition glass-effect mx-auto rounded-xl p-10">
					<h3 class="accent-text heading-subsection text-balanced mb-6">
						{pageContent.problem?.solution?.title}
					</h3>
					<p class="text-text-muted text-lg-lead leading-relaxed">
						{pageContent.problem?.solution?.description}
					</p>
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Vision Section -->
	<FullViewportSection id="vision">
		<div slot="default" class="text-center">
			<div class="mx-auto max-w-6xl">
				<h2 class="section-transition heading-section text-balanced heading-spacing">
					{@html pageContent.vision?.title}
				</h2>
				<p
					class="section-transition text-lg-lead text-text-muted section-spacing mx-auto inline-block max-w-3xl"
				>
					{pageContent.vision?.subtitle}
				</p>

				<!-- Values -->
				<div class="mb-8 grid grid-cols-1 gap-8 md:grid-cols-3">
					{#each pageContent.vision?.values || [] as value, index}
						<div
							class="section-transition glass-effect rounded-xl p-8"
							style="animation-delay: {index * 0.1}s;"
						>
							<div class="mb-6 text-5xl">{value.icon}</div>
							<h3 class="accent-text heading-subsection mb-4">{value.title}</h3>
							<p class="text-text-muted leading-relaxed">{value.description}</p>
						</div>
					{/each}
				</div>

				<!-- Team Section -->
				<div class="section-transition glass-effect rounded-xl p-10">
					<h3 class="heading-subsection text-balanced mb-4">{pageContent.vision?.team?.title}</h3>
					<p class="text-text-muted text-lg-lead mb-8 leading-relaxed">
						{pageContent.vision?.team?.description}
					</p>

					<div class="grid grid-cols-1 gap-8 md:grid-cols-3">
						{#each pageContent.vision?.team?.stats || [] as stat, index}
							<div class="text-center" style="animation-delay: {index * 0.1}s;">
								<div class="accent-text mb-2 text-4xl font-black">
									{stat.number}{stat.suffix}
								</div>
								<div class="text-text-muted font-medium">{stat.label}</div>
							</div>
						{/each}
					</div>
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Demos Section -->
	<FullViewportSection id="demos" className="bg-surface-elevated">
		<div slot="default" class="text-center">
			<div class="mx-auto max-w-6xl">
				<h2 class="section-transition heading-section text-balanced heading-spacing">
					{@html pageContent.demos?.title}
				</h2>
				<p class="section-transition text-lg-lead text-text-muted mx-auto mb-6 max-w-3xl">
					{pageContent.demos?.subtitle}
				</p>
				<p class="section-transition text-accent section-spacing mx-auto max-w-4xl font-medium">
					{pageContent.demos?.description}
				</p>

				<div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
					{#each pageContent.demos?.featured || [] as demo, index}
						<div
							class="section-transition glass-effect group overflow-hidden rounded-xl"
							style="animation-delay: {index * 0.1}s;"
						>
							<div
								class="from-accent/20 to-accent/5 relative flex aspect-video items-center justify-center bg-gradient-to-br"
							>
								<div
									class="text-6xl opacity-50 transition-transform duration-500 group-hover:scale-110"
								>
									🚀
								</div>
								<div
									class="bg-accent/10 absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
								></div>
							</div>

							<div class="p-6">
								<div class="accent-text text-caption mb-3">{demo.category}</div>
								<h3 class="group-hover:accent-text heading-subsection mb-4 transition-colors">
									{demo.title}
								</h3>
								<p class="text-text-muted mb-6 leading-relaxed">{demo.description}</p>

								<div class="mb-6">
									<p class="text-text-muted text-sm leading-relaxed italic">{demo.innovation}</p>
								</div>

								<div class="mb-4">
									<div class="flex flex-wrap gap-2">
										{#each demo.technologies as tech}
											<span
												class="bg-accent/10 text-accent rounded-full px-3 py-1 text-xs font-medium"
												>{tech}</span
											>
										{/each}
									</div>
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Services Section -->
	<FullViewportSection id="services">
		<div slot="default" class="text-center">
			<div class="mx-auto max-w-6xl">
				<h2 class="section-transition heading-section text-balanced heading-spacing">
					What We <span class="accent-text">Create</span>
				</h2>
				<p
					class="section-transition text-lg-lead text-text-muted section-spacing mx-auto max-w-3xl"
				>
					We specialize in experiences that blur the line between digital and physical reality.
				</p>

				<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
					{#each services as service, index}
						<div
							class="section-transition glass-effect group rounded-xl p-6"
							style="animation-delay: {index * 0.1}s;"
						>
							<div class="mb-6 text-4xl transition-transform duration-300 group-hover:scale-110">
								{service.icon}
							</div>
							<h3 class="accent-text heading-subsection mb-4 transition-all duration-300">
								{service.title}
							</h3>
							<p
								class="text-text-muted group-hover:text-text leading-relaxed transition-colors duration-300"
							>
								{service.description}
							</p>
						</div>
					{/each}
				</div>

				<div class="section-transition mt-12">
					<Button href="/services" variant="outline" size="lg">Explore Our Services</Button>
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Traditional Meets Innovation Section -->
	<FullViewportSection id="traditional" className="bg-surface-elevated">
		<div slot="default" class="text-center">
			<div class="mx-auto max-w-6xl">
				<h2 class="section-transition heading-section text-balanced heading-spacing">
					{@html pageContent.traditional?.title}
				</h2>
				<p
					class="section-transition text-lg-lead text-text-muted section-spacing mx-auto max-w-3xl"
				>
					{pageContent.traditional?.subtitle}
				</p>
				<p class="section-transition text-accent section-spacing mx-auto max-w-4xl font-medium">
					{pageContent.traditional?.description}
				</p>

				<!-- Traditional Services -->
				<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
					{#each pageContent.traditional?.services || [] as service, index}
						<div
							class="section-transition glass-effect group rounded-xl p-6"
							style="animation-delay: {index * 0.1}s;"
						>
							<div class="mb-6 text-4xl transition-transform duration-300 group-hover:scale-110">
								{service.icon}
							</div>
							<h3 class="accent-text heading-subsection mb-4 transition-all duration-300">
								{service.title}
							</h3>
							<p
								class="text-text-muted group-hover:text-text leading-relaxed transition-colors duration-300"
							>
								{service.description}
							</p>
						</div>
					{/each}
				</div>
			</div>
		</div>
	</FullViewportSection>

	<!-- Final CTA Section -->
	<FullViewportSection
		id="contact"
		background="linear-gradient(135deg, var(--color-surface) 0%, var(--color-primary) 100%)"
	>
		<div slot="default" class="text-center">
			<!-- Background elements -->
			<div class="pointer-events-none absolute inset-0">
				<div class="bg-accent/5 absolute top-0 left-1/4 h-96 w-96 rounded-full blur-3xl"></div>
				<div
					class="bg-accent-secondary/5 absolute right-1/4 bottom-0 h-64 w-64 rounded-full blur-2xl"
				></div>
			</div>

			<div class="relative z-10 mx-auto max-w-5xl">
				<h2 class="section-transition heading-hero text-balanced heading-spacing">
					{@html pageContent.cta?.title}
				</h2>
				<p
					class="section-transition text-xl-lead text-text-muted section-spacing mx-auto max-w-3xl"
				>
					{pageContent.cta?.subtitle}
				</p>
				<div
					class="section-transition space-y-4 md:flex md:justify-center md:space-y-0 md:space-x-4"
				>
					<Button href={pageContent.cta?.primaryCTA.link} variant="primary" size="lg">
						{pageContent.cta?.primaryCTA.text}
					</Button>
					<Button href={pageContent.cta?.secondaryCTA.link} variant="outline" size="lg">
						{pageContent.cta?.secondaryCTA.text}
					</Button>
				</div>
			</div>
		</div>
	</FullViewportSection>
</div>
