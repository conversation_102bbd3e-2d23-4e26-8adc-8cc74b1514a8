<script lang="ts">
	import Button from '$lib/components/Button.svelte';
	import { page } from '$app/stores';

	// This would typically come from a database or API
	const projects = {
		'digital-art-festival-2024': {
			title: 'Digital Art Festival 2024',
			subtitle: 'Interactive Projection Mapping Installation',
			client: 'Digital Art Festival',
			year: '2024',
			category: 'Projection Mapping',
			duration: '3 months',
			team: '8 people',
			hero: {
				image: '/api/placeholder/1200/800',
				video: null
			},
			overview: 'A large-scale projection mapping installation that transformed the festival\'s main stage into an interactive canvas responding to music and crowd energy. The project combined real-time audio analysis, crowd detection, and dynamic visual generation to create a truly immersive experience.',
			challenge: 'The main challenge was creating a system that could respond to both the music and crowd energy in real-time while maintaining visual coherence across a massive 40-meter wide stage surface. We also had to account for varying lighting conditions and weather during the outdoor festival.',
			solution: 'We developed a custom real-time rendering system that analyzed audio frequencies and crowd movement through computer vision. The system generated dynamic visuals that complemented the music while creating interactive moments when the crowd\'s energy peaked.',
			result: 'The installation was experienced by over 50,000 festival attendees and generated significant social media buzz. The interactive elements encouraged crowd participation and created memorable moments that extended the festival\'s impact beyond the event itself.',
			gallery: [
				'/api/placeholder/800/600',
				'/api/placeholder/800/600',
				'/api/placeholder/800/600',
				'/api/placeholder/800/600',
				'/api/placeholder/800/600',
				'/api/placeholder/800/600'
			],
			technologies: [
				'TouchDesigner',
				'Computer Vision',
				'Audio Analysis',
				'Real-time Rendering',
				'Projection Mapping',
				'Motion Tracking'
			],
			tags: ['Projection Mapping', 'Music Visualization', 'Crowd Interaction', 'Real-time', 'Large Scale'],
			testimonial: {
				quote: "Hitez transformed our festival stage into something magical. The way the visuals responded to both the music and the crowd created an energy we've never experienced before.",
				author: "Sarah Chen",
				role: "Festival Director"
			}
		},
		'brand-experience-center': {
			title: 'Brand Experience Center',
			subtitle: 'AR-Powered Product Showcase',
			client: 'Tech Startup Inc.',
			year: '2024',
			category: 'AR/VR',
			duration: '4 months',
			team: '6 people',
			hero: {
				image: '/api/placeholder/1200/800',
				video: null
			},
			overview: 'An immersive brand experience center featuring AR product visualization, interactive displays, and personalized customer journeys. The space seamlessly blended physical and digital elements to create engaging product demonstrations.',
			challenge: 'Creating a cohesive experience that would work for both tech-savvy early adopters and traditional customers, while showcasing complex technical products in an accessible way.',
			solution: 'We designed a progressive experience that started with familiar touchscreen interfaces and gradually introduced AR elements. Custom AR applications allowed customers to visualize products in their own environments.',
			result: 'The experience center increased customer engagement time by 300% and product understanding scores by 85%. It became a key differentiator in the company\'s sales process.',
			gallery: [
				'/api/placeholder/800/600',
				'/api/placeholder/800/600',
				'/api/placeholder/800/600',
				'/api/placeholder/800/600'
			],
			technologies: [
				'Unity 3D',
				'ARCore/ARKit',
				'Custom CMS',
				'Interactive Displays',
				'Motion Sensors',
				'Cloud Integration'
			],
			tags: ['Augmented Reality', 'Product Visualization', 'Customer Experience', 'Interactive Design'],
			testimonial: {
				quote: "The AR experience center has revolutionized how we present our products. Customers now truly understand our value proposition before they even speak to sales.",
				author: "Michael Rodriguez",
				role: "VP of Marketing"
			}
		}
	};

	const slug = $derived($page.params.slug);
	const project = $derived(projects[slug as keyof typeof projects]);

	// If project doesn't exist, we'll show a 404-like state
	$effect(() => {
		if (!project && slug) {
			// In a real app, you might redirect to a 404 page
			console.log('Project not found:', slug);
		}
	});
</script>

<svelte:head>
	{#if project}
		<title>{project.title} - Hitez</title>
		<meta name="description" content="{project.overview.substring(0, 160)}..." />
	{:else}
		<title>Project Not Found - Hitez</title>
	{/if}
</svelte:head>

{#if project}
	<!-- Hero Section -->
	<section class="pt-32 pb-20 px-6">
		<div class="container mx-auto max-w-6xl">
			<!-- Project Header -->
			<div class="text-center mb-12 slide-up">
				<div class="flex items-center justify-center space-x-4 mb-4">
					<span class="text-accent text-sm font-medium">{project.category}</span>
					<span class="text-text-muted">•</span>
					<span class="text-text-muted text-sm">{project.year}</span>
				</div>
				<h1 class="text-4xl md:text-6xl font-black mb-4">{project.title}</h1>
				<p class="text-xl md:text-2xl text-accent mb-6">{project.subtitle}</p>
				<p class="text-lg text-text-muted max-w-3xl mx-auto">{project.overview}</p>
			</div>
			
			<!-- Hero Image/Video -->
			<div class="slide-up mb-12" style="animation-delay: 0.2s;">
				<div class="aspect-video bg-gradient-to-br from-accent/20 to-accent/5 rounded-lg flex items-center justify-center relative overflow-hidden">
					<div class="text-8xl opacity-30">🎨</div>
					<div class="absolute inset-0 bg-gradient-to-t from-primary/50 to-transparent"></div>
				</div>
			</div>
			
			<!-- Project Details -->
			<div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16 slide-up" style="animation-delay: 0.3s;">
				<div class="text-center">
					<div class="text-2xl font-bold text-accent mb-2">{project.client}</div>
					<div class="text-text-muted text-sm">Client</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-accent mb-2">{project.duration}</div>
					<div class="text-text-muted text-sm">Duration</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-accent mb-2">{project.team}</div>
					<div class="text-text-muted text-sm">Team Size</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-accent mb-2">{project.year}</div>
					<div class="text-text-muted text-sm">Year</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Case Study Content -->
	<section class="py-20 px-6 bg-dark">
		<div class="container mx-auto max-w-4xl">
			<div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
				<!-- Challenge -->
				<div class="slide-up">
					<h2 class="text-3xl font-bold mb-6 text-accent">The Challenge</h2>
					<p class="text-text-muted leading-relaxed">{project.challenge}</p>
				</div>
				
				<!-- Solution -->
				<div class="slide-up" style="animation-delay: 0.1s;">
					<h2 class="text-3xl font-bold mb-6 text-accent">Our Solution</h2>
					<p class="text-text-muted leading-relaxed">{project.solution}</p>
				</div>
				
				<!-- Result -->
				<div class="slide-up" style="animation-delay: 0.2s;">
					<h2 class="text-3xl font-bold mb-6 text-accent">The Result</h2>
					<p class="text-text-muted leading-relaxed">{project.result}</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Gallery -->
	<section class="py-20 px-6">
		<div class="container mx-auto max-w-6xl">
			<div class="text-center mb-16 slide-up">
				<h2 class="text-4xl md:text-5xl font-bold mb-6">Project Gallery</h2>
				<p class="text-xl text-text-muted">A closer look at the experience in action</p>
			</div>
			
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{#each project.gallery as image, index}
					<div class="slide-up" style="animation-delay: {index * 0.1}s;">
						<div class="aspect-square bg-gradient-to-br from-accent/20 to-accent/5 rounded-lg flex items-center justify-center relative overflow-hidden group cursor-pointer">
							<div class="text-4xl opacity-30 group-hover:scale-110 transition-transform duration-500">🖼️</div>
							<div class="absolute inset-0 bg-accent/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
						</div>
					</div>
				{/each}
			</div>
		</div>
	</section>

	<!-- Technologies & Testimonial -->
	<section class="py-20 px-6 bg-dark">
		<div class="container mx-auto max-w-6xl">
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
				<!-- Technologies -->
				<div class="slide-up">
					<h2 class="text-3xl font-bold mb-6 text-accent">Technologies Used</h2>
					<div class="flex flex-wrap gap-3">
						{#each project.technologies as tech}
							<span class="bg-accent/20 text-accent px-4 py-2 rounded-full text-sm font-medium">
								{tech}
							</span>
						{/each}
					</div>
				</div>
				
				<!-- Testimonial -->
				<div class="slide-up" style="animation-delay: 0.1s;">
					<h2 class="text-3xl font-bold mb-6 text-accent">Client Feedback</h2>
					<blockquote class="text-lg text-text-muted italic mb-4 leading-relaxed">
						"{project.testimonial.quote}"
					</blockquote>
					<div class="flex items-center">
						<div>
							<div class="font-bold text-accent">{project.testimonial.author}</div>
							<div class="text-text-muted text-sm">{project.testimonial.role}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Navigation & CTA -->
	<section class="py-20 px-6">
		<div class="container mx-auto max-w-4xl">
			<div class="text-center slide-up">
				<h2 class="text-4xl font-bold mb-6">
					Inspired by This <span class="text-accent">Project?</span>
				</h2>
				<p class="text-xl text-text-muted mb-8 max-w-2xl mx-auto">
					Let's discuss how we can create something equally extraordinary for your brand.
				</p>
				<div class="space-y-4 md:space-y-0 md:space-x-4 md:flex md:justify-center mb-12">
					<Button href="/contact" variant="primary" size="lg">
						Start Your Project
					</Button>
					<Button href="/work" variant="outline" size="lg">
						View More Work
					</Button>
				</div>
			</div>
		</div>
	</section>
{:else}
	<!-- 404 State -->
	<section class="pt-32 pb-20 px-6 min-h-screen flex items-center justify-center">
		<div class="text-center">
			<div class="text-8xl mb-8 opacity-50">🔍</div>
			<h1 class="text-4xl font-bold mb-4">Project Not Found</h1>
			<p class="text-xl text-text-muted mb-8">
				The project you're looking for doesn't exist or has been moved.
			</p>
			<div class="space-y-4 md:space-y-0 md:space-x-4 md:flex md:justify-center">
				<Button href="/work" variant="primary">
					View All Projects
				</Button>
				<Button href="/" variant="outline">
					Go Home
				</Button>
			</div>
		</div>
	</section>
{/if}
